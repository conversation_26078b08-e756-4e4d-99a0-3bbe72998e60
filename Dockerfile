# Use official Python base image
FROM python:3.11-slim
 
# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
 
# Set working directory
WORKDIR /app
 
# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    curl \
&& rm -rf /var/lib/apt/lists/*
 
# Install pipenv or requirements
COPY requirements.txt .
RUN pip install --upgrade pip
RUN pip install -r requirements.txt
 
# Copy app source code
COPY . .

COPY .env .env.staging

# Set default environment file
ENV ENV_FILE=.env.staging
 
# Install Uvicorn for live reloading
RUN pip install uvicorn[standard]
 
# Expose the FastAPI port
EXPOSE 8000
 
# Run the app with live reload
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--env-file", ".env.staging"]
