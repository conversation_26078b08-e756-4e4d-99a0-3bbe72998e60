# Database Configuration

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=xKylnVNyXSRZ3+3BFDWHaGApfOoT9Uq1yhqhGDPx
AWS_REGION=ap-southeast-2
S3_BUCKET_NAME=growthhive-stage
S3_BASE_URL=https://growthhive-stage.s3.ap-southeast-2.amazonaws.com

# Development Settings
ENVIRONMENT=staging
DEBUG=true

# File Upload Settings
MAX_FILE_SIZE=10485760
UPLOAD_DIR=uploads

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/growthhive.log

# Database URL - Local Development
DATABASE_URL=postgresql+asyncpg://growthhive_stage:<EMAIL>:5432/growthhive_stage


# Zoho CRM Integration Settings - Australian Domain
ZOHO_CLIENT_ID=1000.PRYTZ0VSO7PV6KL8ZU0TLLMOF8BU5A
ZOHO_CLIENT_SECRET=d2891e7f56126961192d2c98856add5e3f6a7082be
ZOHO_REFRESH_TOKEN=**********************************************************************
ZOHO_BASE_URL=https://www.zohoapis.com.au/crm/v2
ZOHO_AUTH_URL=https://accounts.zoho.com.au/oauth/v2/token
ZOHO_SYNC_INTERVAL_MINUTES=15

# OpenAI Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_MODEL=gpt-4-turbo
EMBEDDING_MODEL=text-embedding-3-small


# DocQA Configuration
FAISS_INDEX_PATH=./data/faiss_index
CHUNK_SIZE=400
CHUNK_OVERLAP=50
TOP_K_RETRIEVAL=6
TOP_K=6
SIMILARITY_THRESHOLD=0.7
MAX_TOKENS_RESPONSE=1000
MAX_TOKENS=1000
TEMPERATURE=0.1
CHAT_MODEL=gpt-4-turbo

# Additional DocQA Settings
MAX_FILE_SIZE_MB=100
TESSERACT_CMD=tesseract
OCR_LANGUAGES=eng
TEMP_DIR=/tmp/docqa

# Task Processing Configuration
DOCUMENT_PROCESSING_QUEUE=document_processing
DOCUMENT_PROCESSING_RETRY_DELAY=60
DOCUMENT_PROCESSING_MAX_RETRIES=3

# LangGraph Configuration
LANGGRAPH_VERBOSE=true
LANGGRAPH_CHECKPOINT_ENABLED=true
LANGGRAPH_MAX_EXECUTION_TIME=300
LANGGRAPH_RECURSION_LIMIT=50

# Agent Configuration
AGENT_TEMPERATURE=0.1
AGENT_MAX_TOKENS=1000
AGENT_TIMEOUT=30

# Lead Qualification Configuration
LEAD_QUALIFICATION_THRESHOLD=0.7
LEAD_STATUS_AUTO_UPDATE=true
MEETING_BOOKING_AUTO_QUALIFY=true

# Prompt Management
PROMPT_TEMPLATES_PATH=./app/agents/prompts
PROMPT_VERSION=v1.0

RABBITMQ_URL=amqp://guest:guest@rabbitmq:5672/
CELERY_BROKER_URL= amqp://guest:guest@rabbitmq:5672/
CELERY_RESULT_BACKEND=redis://3.111.65.249:6379/0
REDIS_URL=redis://3.111.65.249:6379/1
REDIS_SESSION_DB=2
REDIS_CACHE_DB=3
