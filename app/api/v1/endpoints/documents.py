"""
Document API endpoints
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Form

from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.factory import get_document_service
from app.core.responses import create_success_response, create_error_response, ErrorCodes
from app.schemas.document import (
    DocumentUploadRequest,
    DocumentStatusUpdateRequest
)
from app.services.document_service import DocumentService

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/",
    response_model=dict,
    responses={
        200: {
            "description": "Documents retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Documents Retrieved",
                            "description": "Successfully retrieved 2 documents"
                        },
                        "data": {
                            "items": [
                                {
                                    "id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
                                    "name": "GrowthHive Signed Agreement",
                                    "description": "Official signed agreement document for GrowthHive project",
                                    "file_type": "application/pdf",
                                    "file_size": "7203206",
                                    "file_path": "growthhive/document/20250627_162319_e45dde0ae56f.pdf",
                                    "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162319_e45dde0ae56f.pdf",
                                    "user_id": "913fe36e-bfd2-4e3d-9b1d-ba186b22f4f9",
                                    "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
                                    "is_active": True,
                                    "is_deleted": False,
                                    "created_at": "2025-06-27T16:23:20.742000+00:00",
                                    "updated_at": "2025-06-27T16:28:01.371000+00:00",
                                    "deleted_at": None
                                }
                            ],
                            "total_count": 2,
                            "pagination": {
                                "page": 1,
                                "limit": 20,
                                "total": 2,
                                "pages": 1
                            }
                        },
                        "error_code": None
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized - Invalid or missing authentication token",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication token is required"
                        },
                        "data": None,
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        },
        500: {
            "description": "Internal server error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Error Retrieving Documents",
                            "description": "Database connection failed"
                        },
                        "data": None,
                        "error_code": "DATABASE_ERROR"
                    }
                }
            }
        }
    }
)
async def get_documents(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search term"),
    franchisor_id: Optional[str] = Query(None, description="Filter by franchisor ID"),
    file_type: Optional[str] = Query(None, description="Filter by file type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Get documents with filtering, search, and pagination"""
    try:
        result = await document_service.get_documents_with_pagination(
            skip=skip,
            limit=limit,
            search=search,
            user_id=current_user.get("user_id"),
            franchisor_id=franchisor_id,
            file_type=file_type,
            is_active=is_active
        )
        
        return create_success_response(
            data=result.model_dump(),
            message_title="Documents Retrieved",
            message_description=f"Successfully retrieved {len(result.items)} documents"
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Retrieving Documents",
            message_description=str(e),
            status_code=500
        )


@router.get("/{document_id}",
    response_model=dict,
    responses={
        200: {
            "description": "Document retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Document Retrieved",
                            "description": "Document retrieved successfully"
                        },
                        "data": {
                            "id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
                            "name": "GrowthHive Signed Agreement",
                            "description": "Official signed agreement document for GrowthHive project",
                            "file_type": "application/pdf",
                            "file_size": "7203206",
                            "file_path": "growthhive/document/20250627_162319_e45dde0ae56f.pdf",
                            "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162319_e45dde0ae56f.pdf",
                            "user_id": "913fe36e-bfd2-4e3d-9b1d-ba186b22f4f9",
                            "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
                            "is_active": True,
                            "is_deleted": False,
                            "created_at": "2025-06-27T16:23:20.742000+00:00",
                            "updated_at": "2025-06-27T16:28:01.371000+00:00",
                            "deleted_at": None
                        },
                        "error_code": None
                    }
                }
            }
        },
        404: {
            "description": "Document not found",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Document Not Found",
                            "description": "Document with ID cdf6651e-f28b-4dfa-8048-0bef028b5d9d not found"
                        },
                        "data": None,
                        "error_code": "NOT_FOUND"
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication token is required"
                        },
                        "data": None,
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        }
    }
)
async def get_document(
    document_id: str,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Get document by ID"""
    try:
        document = await document_service.get_document_by_id(document_id)
        
        if not document:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description=f"Document with ID {document_id} not found",
                status_code=404
            )
        
        return create_success_response(
            data=document.model_dump(),
            message_title="Document Retrieved",
            message_description="Document retrieved successfully"
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Retrieving Document",
            message_description=str(e),
            status_code=500
        )





@router.post("/upload",
    response_model=dict,
    responses={
        201: {
            "description": "Document uploaded successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Document Uploaded",
                            "description": "Document uploaded successfully"
                        },
                        "data": {
                            "id": "ed8b831d-b082-4eea-962e-65082f2b1ef3",
                            "name": "Franchisor Agreement Document",
                            "description": "Agreement document linked to specific franchisor",
                            "file_type": "application/pdf",
                            "file_size": "7203206",
                            "file_path": "growthhive/document/20250627_162709_bdb21ea1656a.pdf",
                            "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162709_bdb21ea1656a.pdf",
                            "user_id": "913fe36e-bfd2-4e3d-9b1d-ba186b22f4f9",
                            "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
                            "is_active": True,
                            "is_deleted": False,
                            "created_at": "2025-06-27T16:27:10.859000+00:00",
                            "updated_at": "2025-06-27T16:27:10.859000+00:00",
                            "deleted_at": None
                        },
                        "error_code": None
                    }
                }
            }
        },
        400: {
            "description": "Invalid file or validation error",
            "content": {
                "application/json": {
                    "examples": {
                        "no_file": {
                            "summary": "No file provided",
                            "value": {
                                "success": False,
                                "message": {
                                    "title": "Invalid File",
                                    "description": "No file provided"
                                },
                                "data": None,
                                "error_code": "VALIDATION_ERROR"
                            }
                        },
                        "invalid_file_type": {
                            "summary": "Invalid file type",
                            "value": {
                                "success": False,
                                "message": {
                                    "title": "Upload Failed",
                                    "description": "File type not allowed. Allowed types: .pdf, .png, .docx, .jpg, .doc, .jpeg"
                                },
                                "data": None,
                                "error_code": "UPLOAD_ERROR"
                            }
                        }
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication token is required"
                        },
                        "data": None,
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        },
        500: {
            "description": "Upload failed",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Upload Failed",
                            "description": "S3 service not available"
                        },
                        "data": None,
                        "error_code": "UPLOAD_ERROR"
                    }
                }
            }
        }
    }
)
async def upload_document(
    file: UploadFile = File(...),
    name: str = Form(...),
    description: Optional[str] = Form(None),
    franchisor_id: Optional[str] = Form(None),
    is_active: bool = Form(True),
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Upload a document file"""
    try:
        # Validate file
        if not file.filename:
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Invalid File",
                message_description="No file provided",
                status_code=400
            )

        # Validate file type - only PDF files allowed
        if not file.filename.lower().endswith('.pdf'):
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="Invalid File Type",
                message_description="Only PDF files are allowed for document uploads",
                status_code=400
            )

        # Validate franchisor_id if provided
        validated_franchisor_id = None
        if franchisor_id and franchisor_id.strip() and franchisor_id.lower() != 'none' and franchisor_id != 'string':
            try:
                import uuid
                # Try to parse as UUID to validate format
                uuid.UUID(franchisor_id)
                validated_franchisor_id = franchisor_id
            except ValueError:
                return create_error_response(
                    error_code=ErrorCodes.VALIDATION_ERROR,
                    message_title="Invalid Franchisor ID",
                    message_description="Franchisor ID must be a valid UUID format",
                    status_code=400
                )

        # Create upload request
        upload_data = DocumentUploadRequest(
            name=name,
            description=description,
            franchisor_id=validated_franchisor_id,
            is_active=is_active
        )
        
        # Upload file and create document
        document = await document_service.upload_file(file, upload_data, current_user.get("user_id"))
        
        return create_success_response(
            data=document.model_dump(),
            message_title="Document Uploaded",
            message_description="Document uploaded successfully",
            status_code=201
        )
        
    except HTTPException as e:
        return create_error_response(
            error_code=ErrorCodes.UPLOAD_ERROR,
            message_title="Upload Failed",
            message_description=e.detail,
            status_code=e.status_code
        )
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.UPLOAD_ERROR,
            message_title="Upload Failed",
            message_description=str(e),
            status_code=500
        )


@router.get("/{document_id}/processing-status",
    response_model=dict,
    responses={
        200: {
            "description": "Processing status retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Processing Status",
                            "description": "Document processing status retrieved successfully"
                        },
                        "data": {
                            "document_id": "5f5d237d-6fa2-4b71-bcb6-a9bb8679d7c7",
                            "status": "processing",
                            "progress": 75,
                            "message": "Generating embeddings and storing vectors...",
                            "error": None,
                            "started_at": "2025-07-09T11:18:00.000Z",
                            "completed_at": None,
                            "is_processing": True
                        },
                        "error_code": None
                    }
                }
            }
        },
        404: {
            "description": "Document not found",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Document Not Found",
                            "description": "Document with specified ID not found"
                        },
                        "data": None,
                        "error_code": "NOT_FOUND"
                    }
                }
            }
        }
    }
)
async def get_document_processing_status(
    document_id: str,
    current_user: dict = Depends(get_current_user)
):
    """Get document processing status"""
    try:
        from app.services.async_document_processor import get_async_document_processor
        processor = get_async_document_processor()

        status = await processor.get_processing_status(document_id)

        if status is None:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description="Document with specified ID not found",
                status_code=404
            )

        return create_success_response(
            data=status,
            message_title="Processing Status",
            message_description="Document processing status retrieved successfully",
            status_code=200
        )

    except Exception as e:
        logger.error(f"Error getting processing status for document {document_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Status Check Failed",
            message_description=str(e),
            status_code=500
        )


@router.post("/{document_id}/reprocess",
    response_model=dict,
    responses={
        200: {
            "description": "Document reprocessing started successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Reprocessing Started",
                            "description": "Document reprocessing started successfully"
                        },
                        "data": {
                            "document_id": "5f5d237d-6fa2-4b71-bcb6-a9bb8679d7c7",
                            "status": "processing",
                            "message": "Reprocessing started"
                        },
                        "error_code": None
                    }
                }
            }
        },
        400: {
            "description": "Document already being processed",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Already Processing",
                            "description": "Document is already being processed"
                        },
                        "data": None,
                        "error_code": "VALIDATION_ERROR"
                    }
                }
            }
        }
    }
)
async def reprocess_document(
    document_id: str,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Manually trigger document reprocessing"""
    try:
        # Get document to verify it exists and get file path
        document = await document_service.get_document_by_id(document_id)
        if not document:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description="Document with specified ID not found",
                status_code=404
            )

        # Document reprocessing disabled for document management
        return create_error_response(
            error_code=ErrorCodes.VALIDATION_ERROR,
            message_title="Processing Disabled",
            message_description="Document ingestion and processing is disabled for document management uploads",
            status_code=400
        )

    except Exception as e:
        logger.error(f"Error reprocessing document {document_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Reprocessing Failed",
            message_description=str(e),
            status_code=500
        )








@router.delete("/{document_id}",
    response_model=dict,
    responses={
        200: {
            "description": "Document deleted successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "message": {
                            "title": "Document Deleted",
                            "description": "Document deleted successfully"
                        },
                        "data": {
                            "document_id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
                            "deleted": True
                        },
                        "error_code": None
                    }
                }
            }
        },
        404: {
            "description": "Document not found",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Document Not Found",
                            "description": "Document with ID cdf6651e-f28b-4dfa-8048-0bef028b5d9d not found"
                        },
                        "data": None,
                        "error_code": "NOT_FOUND"
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication token is required"
                        },
                        "data": None,
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        },
        500: {
            "description": "Delete failed",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Error Deleting Document",
                            "description": "Failed to delete document"
                        },
                        "data": None,
                        "error_code": "DATABASE_ERROR"
                    }
                }
            }
        }
    }
)
async def delete_document(
    document_id: str,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Delete document (soft delete)"""
    try:
        success = await document_service.delete_document(document_id)
        
        if not success:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description=f"Document with ID {document_id} not found",
                status_code=404
            )
        
        return create_success_response(
            data={"document_id": document_id, "deleted": True},
            message_title="Document Deleted",
            message_description="Document deleted successfully"
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Deleting Document",
            message_description=str(e),
            status_code=500
        )





@router.patch("/{document_id}/status",
    response_model=dict,
    responses={
        200: {
            "description": "Document status updated successfully",
            "content": {
                "application/json": {
                    "examples": {
                        "activated": {
                            "summary": "Document activated",
                            "value": {
                                "success": True,
                                "message": {
                                    "title": "Document Status Updated",
                                    "description": "Document activated successfully"
                                },
                                "data": {
                                    "id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
                                    "name": "GrowthHive Signed Agreement",
                                    "description": "Official signed agreement document for GrowthHive project",
                                    "file_type": "application/pdf",
                                    "file_size": "7203206",
                                    "file_path": "growthhive/document/20250627_162319_e45dde0ae56f.pdf",
                                    "file_url": "https://openxcell-development-public.s3.ap-south-1.amazonaws.com/growthhive/document/20250627_162319_e45dde0ae56f.pdf",
                                    "user_id": "913fe36e-bfd2-4e3d-9b1d-ba186b22f4f9",
                                    "franchisor_id": "a6fc7a0e-f1ee-431f-a006-90452457cd1f",
                                    "is_active": True,
                                    "is_deleted": False,
                                    "created_at": "2025-06-27T16:23:20.742000+00:00",
                                    "updated_at": "2025-06-27T16:30:01.371000+00:00",
                                    "deleted_at": None
                                },
                                "error_code": None
                            }
                        },
                        "deactivated": {
                            "summary": "Document deactivated",
                            "value": {
                                "success": True,
                                "message": {
                                    "title": "Document Status Updated",
                                    "description": "Document deactivated successfully"
                                },
                                "data": {
                                    "id": "cdf6651e-f28b-4dfa-8048-0bef028b5d9d",
                                    "name": "GrowthHive Signed Agreement",
                                    "is_active": False,
                                    "updated_at": "2025-06-27T16:30:01.371000+00:00"
                                },
                                "error_code": None
                            }
                        }
                    }
                }
            }
        },
        404: {
            "description": "Document not found",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Document Not Found",
                            "description": "Document with ID cdf6651e-f28b-4dfa-8048-0bef028b5d9d not found"
                        },
                        "data": None,
                        "error_code": "NOT_FOUND"
                    }
                }
            }
        },
        400: {
            "description": "Validation error",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Validation Error",
                            "description": "is_active field is required"
                        },
                        "data": None,
                        "error_code": "VALIDATION_ERROR"
                    }
                }
            }
        },
        401: {
            "description": "Unauthorized",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Authentication Required",
                            "description": "Valid authentication token is required"
                        },
                        "data": None,
                        "error_code": "UNAUTHORIZED"
                    }
                }
            }
        },
        500: {
            "description": "Status update failed",
            "content": {
                "application/json": {
                    "example": {
                        "success": False,
                        "message": {
                            "title": "Error Updating Document Status",
                            "description": "Failed to update document status"
                        },
                        "data": None,
                        "error_code": "DATABASE_ERROR"
                    }
                }
            }
        }
    }
)
async def update_document_status(
    document_id: str,
    status_data: DocumentStatusUpdateRequest,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Update document active/inactive status"""
    try:
        document = await document_service.update_document_status(document_id, status_data)
        
        if not document:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description=f"Document with ID {document_id} not found",
                status_code=404
            )
        
        status_text = "activated" if status_data.is_active else "deactivated"
        
        return create_success_response(
            data=document.model_dump(),
            message_title="Document Status Updated",
            message_description=f"Document {status_text} successfully"
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Updating Document Status",
            message_description=str(e),
            status_code=500
        )
