"""
Database configuration for GrowthHive API
Single source of truth for database configuration
"""

import asyncio
import ssl
from typing import AsyncGenerator
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.pool import Null<PERSON><PERSON>
from urllib.parse import urlparse, parse_qs, urlencode
from app.core.config.settings import settings
from app.core.logging import logger

# Get database URL from settings
DATABASE_URL = settings.DATABASE_URL

def clean_database_url(url: str) -> str:
    """
    Clean database URL by removing unsupported parameters for asyncpg.
    asyncpg doesn't support sslmode parameter directly.
    """
    if not url.startswith('postgresql+asyncpg://'):
        return url
    
    # Parse the URL
    parsed = urlparse(url)
    query_params = parse_qs(parsed.query)
    
    # Remove unsupported parameters for asyncpg
    unsupported_params = ['sslmode', 'sslcert', 'sslkey', 'sslrootcert']
    for param in unsupported_params:
        if param in query_params:
            del query_params[param]
            logger.warning(f"Removed unsupported parameter '{param}' from DATABASE_URL for asyncpg")
    
    # Rebuild the URL without unsupported parameters
    clean_query = urlencode(query_params, doseq=True) if query_params else ""
    clean_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}"
    if clean_query:
        clean_url += f"?{clean_query}"
    
    return clean_url

# Clean the database URL
CLEAN_DATABASE_URL = clean_database_url(DATABASE_URL)

# Create async engine with improved configuration for production environments
try:
    # Enhanced connection arguments for better reliability and timeout handling
    connect_args = {
        "command_timeout": settings.DB_COMMAND_TIMEOUT,
        "server_settings": {
            "application_name": "growthhive_api",
            "jit": "off",  # Disable JIT for better compatibility
            "statement_timeout": str(settings.DB_COMMAND_TIMEOUT * 1000),  # Convert to milliseconds
            "lock_timeout": "30000",  # 30 seconds lock timeout
            "idle_in_transaction_session_timeout": "60000"  # 60 seconds idle timeout
        }
    }

    # Enhanced SSL configuration for AWS RDS and other cloud databases
    if any(cloud_provider in CLEAN_DATABASE_URL.lower() for cloud_provider in ["rds.amazonaws.com", "cloudsql.googleapis.com", "azure.com"]):
        # For AWS RDS, we need more specific SSL configuration
        if "rds.amazonaws.com" in CLEAN_DATABASE_URL.lower():
            # AWS RDS specific configuration - try multiple SSL approaches
            try:
                # First try with SSL context
                ssl_context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
                ssl_context.check_hostname = False
                ssl_context.verify_mode = ssl.CERT_NONE
                
                connect_args.update({
                    "ssl": ssl_context,
                    "timeout": settings.DB_CONNECTION_TIMEOUT,  # Connection timeout
                })
                logger.info("🔒 SSL configuration enabled for AWS RDS with SSL context")
            except Exception as ssl_error:
                logger.warning(f"⚠️ SSL context creation failed, falling back to basic SSL: {ssl_error}")
                # Fallback to basic SSL
                connect_args.update({
                    "ssl": True,
                    "timeout": settings.DB_CONNECTION_TIMEOUT,  # Connection timeout
                })
                logger.info("🔒 SSL configuration enabled for AWS RDS with basic SSL")
        else:
            # Other cloud providers
            connect_args.update({
                "ssl": "prefer"  # Use SSL if available, but don't require it (asyncpg format)
            })
            logger.info("🔒 SSL configuration enabled for cloud database (prefer mode)")
    else:
        # Local development - no SSL required
        logger.info("🔓 No SSL configuration for local development")

    # Create engine with improved settings for production
    async_engine = create_async_engine(
        CLEAN_DATABASE_URL,
        echo=settings.DB_ECHO,
        poolclass=NullPool,  # Use NullPool to avoid connection pooling issues
        pool_pre_ping=True,  # Verify connections before use
        connect_args=connect_args
    )
    logger.info("✅ SQLAlchemy async engine created successfully with enhanced timeout settings")
except Exception as e:
    logger.error(f"⚠️ SQLAlchemy async engine creation failed: {e}")
    async_engine = None

# Session maker
AsyncSessionLocal = sessionmaker(
    async_engine,
    class_=AsyncSession,
    expire_on_commit=False
) if async_engine else None

# Base class for models
Base = declarative_base()

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Get async database session with improved timeout and retry handling"""
    if not AsyncSessionLocal:
        raise RuntimeError("Database engine not initialized")

    session = None
    try:
        # Create session with timeout
        async with asyncio.timeout(settings.DB_CONNECTION_TIMEOUT):
            session = AsyncSessionLocal()
            yield session
    except asyncio.TimeoutError:
        logger.error("❌ Database connection timeout")
        raise RuntimeError("Database connection timeout")
    except Exception as e:
        logger.error(f"⚠️ Database session error: {e}")
        raise
    finally:
        if session:
            try:
                await session.close()
            except Exception as e:
                logger.warning(f"⚠️ Error closing database session: {e}")

# --- Model imports moved to avoid circular imports ---
# Models are imported in alembic/env.py for migration discovery
# ---------------------------------------------------------------
