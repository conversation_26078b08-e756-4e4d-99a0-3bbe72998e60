"""
Enhanced OpenAI Service with Vision and Batch Processing

This service provides comprehensive OpenAI integration including:
- GPT-4 Vision for chart detection and captioning
- Batch embedding generation for performance
- Text completion and streaming
- Image analysis and OCR enhancement
"""

import base64
import io
import time
from typing import List, Dict, Any, Optional
import structlog
import openai
from PIL import Image

from ..config import get_config
from ..types import DocQAException

logger = structlog.get_logger()


class EnhancedOpenAIService:
    """
    Enhanced OpenAI service with vision capabilities and batch processing
    """
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the enhanced OpenAI service

        Args:
            api_key: OpenAI API key (uses config if not provided)
        """
        config = get_config()
        self.api_key = api_key or config.openai_api_key
        self.client = openai.OpenAI(api_key=self.api_key)

        # Model configurations
        self.embedding_model = config.embedding_model
        self.chat_model = config.chat_model
        self.vision_model = "gpt-4o"  # Latest vision model
        
        # Performance settings
        self.max_batch_size = 100  # Maximum embeddings per batch
        self.max_concurrent_requests = 10
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.1  # Minimum seconds between requests
        
        logger.info("Enhanced OpenAI service initialized",
                   embedding_model=self.embedding_model,
                   chat_model=self.chat_model,
                   vision_model=self.vision_model)
    
    def _rate_limit(self):
        """Simple rate limiting to avoid API limits"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for a single text
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector
        """
        try:
            self._rate_limit()
            
            response = self.client.embeddings.create(
                model=self.embedding_model,
                input=text.strip()
            )
            
            return response.data[0].embedding
            
        except Exception as e:
            logger.error("Embedding generation failed", 
                        text_length=len(text), error=str(e))
            raise DocQAException(f"Failed to generate embedding: {str(e)}")
    
    def generate_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """
        Generate embeddings for multiple texts in batches
        
        Args:
            texts: List of texts to embed
            
        Returns:
            List of embedding vectors
        """
        if not texts:
            return []
        
        try:
            logger.info("Generating batch embeddings", count=len(texts))
            
            all_embeddings = []
            
            # Process in batches to avoid API limits
            for i in range(0, len(texts), self.max_batch_size):
                batch = texts[i:i + self.max_batch_size]
                
                self._rate_limit()
                
                response = self.client.embeddings.create(
                    model=self.embedding_model,
                    input=[text.strip() for text in batch]
                )
                
                batch_embeddings = [data.embedding for data in response.data]
                all_embeddings.extend(batch_embeddings)
                
                logger.debug("Batch processed", 
                           batch_size=len(batch),
                           total_processed=len(all_embeddings))
            
            logger.info("Batch embedding generation completed", 
                       total_embeddings=len(all_embeddings))
            
            return all_embeddings
            
        except Exception as e:
            logger.error("Batch embedding generation failed", 
                        count=len(texts), error=str(e))
            raise DocQAException(f"Failed to generate batch embeddings: {str(e)}")
    
    def analyze_image_with_gpt4_vision(
        self,
        image_base64: str,
        prompt: str,
        max_tokens: int = 1000
    ) -> Optional[str]:
        """
        Analyze image using GPT-4 Vision
        
        Args:
            image_base64: Base64 encoded image
            prompt: Analysis prompt
            max_tokens: Maximum tokens in response
            
        Returns:
            Analysis result or None if failed
        """
        try:
            self._rate_limit()
            
            response = self.client.chat.completions.create(
                model=self.vision_model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/png;base64,{image_base64}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=max_tokens,
                temperature=0.1  # Low temperature for consistent analysis
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error("Vision analysis failed", error=str(e))
            return None
    
    def detect_and_caption_chart(self, image: Image.Image) -> Optional[Dict[str, Any]]:
        """
        Detect and caption charts in images
        
        Args:
            image: PIL Image object
            
        Returns:
            Chart information or None if not a chart
        """
        try:
            # Convert image to base64
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            img_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            # Specialized chart detection prompt
            prompt = """
            Analyze this image and determine if it contains a chart, graph, diagram, or visual data representation.
            
            If it contains a chart/graph, provide a JSON response with:
            {
                "is_chart": true,
                "chart_type": "bar/line/pie/scatter/other",
                "title": "chart title if visible",
                "description": "detailed description of data and trends",
                "key_insights": ["insight 1", "insight 2"],
                "data_points": "summary of key data points",
                "confidence": 0.8
            }
            
            If it's NOT a chart, respond with:
            {
                "is_chart": false,
                "content_type": "text/photo/diagram/other",
                "description": "brief description of what the image contains"
            }
            """
            
            response = self.analyze_image_with_gpt4_vision(
                image_base64=img_base64,
                prompt=prompt,
                max_tokens=800
            )
            
            if response:
                # Try to parse JSON response
                try:
                    import json
                    result = json.loads(response)
                    
                    if result.get('is_chart', False):
                        return {
                            'type': 'chart',
                            'chart_type': result.get('chart_type', 'unknown'),
                            'title': result.get('title', ''),
                            'caption': result.get('description', ''),
                            'insights': result.get('key_insights', []),
                            'data_points': result.get('data_points', ''),
                            'confidence': result.get('confidence', 0.7),
                            'size': image.size
                        }
                    else:
                        # Not a chart, but might be useful content
                        return {
                            'type': 'image',
                            'content_type': result.get('content_type', 'other'),
                            'description': result.get('description', ''),
                            'confidence': 0.5,
                            'size': image.size
                        }
                        
                except json.JSONDecodeError:
                    # Fallback to text parsing
                    if "is_chart" in response.lower() and "true" in response.lower():
                        return {
                            'type': 'chart',
                            'caption': response,
                            'confidence': 0.6,
                            'size': image.size
                        }
            
            return None
            
        except Exception as e:
            logger.debug("Chart detection failed", error=str(e))
            return None
    
    def enhance_ocr_text(self, ocr_text: str, image: Image.Image) -> str:
        """
        Enhance OCR text using GPT-4 Vision for better accuracy
        
        Args:
            ocr_text: Raw OCR text
            image: Original image
            
        Returns:
            Enhanced text
        """
        try:
            if not ocr_text.strip():
                return ocr_text
            
            # Convert image to base64
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            img_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            prompt = f"""
            I have extracted the following text from an image using OCR:
            
            "{ocr_text}"
            
            Please review the image and the OCR text, then provide:
            1. Corrected text with proper spelling and formatting
            2. Any additional text that OCR might have missed
            3. Context about the document structure (headers, lists, etc.)
            
            Focus on accuracy and readability. If the OCR text looks correct, just confirm it.
            """
            
            enhanced_text = self.analyze_image_with_gpt4_vision(
                image_base64=img_base64,
                prompt=prompt,
                max_tokens=1000
            )
            
            return enhanced_text or ocr_text
            
        except Exception as e:
            logger.debug("OCR enhancement failed", error=str(e))
            return ocr_text
    
    def generate_text_completion(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1
    ) -> str:
        """
        Generate text completion using chat model
        
        Args:
            prompt: Input prompt
            max_tokens: Maximum tokens in response
            temperature: Sampling temperature
            
        Returns:
            Generated text
        """
        try:
            self._rate_limit()
            
            response = self.client.chat.completions.create(
                model=self.chat_model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logger.error("Text completion failed", error=str(e))
            raise DocQAException(f"Failed to generate text completion: {str(e)}")
    
    def stream_text_completion(
        self,
        prompt: str,
        max_tokens: int = 1000,
        temperature: float = 0.1
    ):
        """
        Stream text completion using chat model
        
        Args:
            prompt: Input prompt
            max_tokens: Maximum tokens in response
            temperature: Sampling temperature
            
        Yields:
            Text chunks as they are generated
        """
        try:
            self._rate_limit()
            
            stream = self.client.chat.completions.create(
                model=self.chat_model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_tokens,
                temperature=temperature,
                stream=True
            )
            
            for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error("Streaming completion failed", error=str(e))
            yield f"Error: {str(e)}"
