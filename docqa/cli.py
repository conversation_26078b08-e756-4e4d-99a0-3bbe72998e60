"""
DocQA CLI interface using Typer
"""

import sys
from typing import Optional
import typer
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich.markdown import Markdown
import structlog

from .ingest import DocumentIngestionService
from .serve import ask_question, ask_question_stream, health_check, get_question_context
from .config import get_config
from .types import TableName

# Initialize rich console
console = Console()

# Initialize typer app
app = typer.Typer(
    name="docqa",
    help="DocQA - Document Question Answering System with pgvector",
    add_completion=False
)

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


@app.command()
def ingest(
    source: str = typer.Argument(..., help="S3 URL, file path, or HTTP URL to ingest"),
    table: Optional[TableName] = typer.Option(
        None, 
        "--table", 
        help="Force ingestion to specific table (documents or franchisors)"
    ),
    translate: bool = typer.Option(
        False, 
        "--translate", 
        help="Translate content to English if needed"
    ),
    extract_charts: bool = typer.Option(
        True, 
        "--extract-charts/--no-extract-charts", 
        help="Extract and analyze charts using GPT-4 Vision"
    ),
    verbose: bool = typer.Option(
        False, 
        "--verbose", 
        "-v", 
        help="Enable verbose output"
    )
):
    """
    Ingest a document from S3, file path, or HTTP URL.
    
    Examples:
        docqa ingest s3://bucket/document.pdf
        docqa ingest /path/to/document.docx --translate
        docqa ingest https://example.com/brochure.pdf --table franchisors
    """
    try:
        console.print(f"🔄 Starting ingestion of: [bold]{source}[/bold]")
        
        if verbose:
            console.print("Configuration:")
            console.print(f"  - Target table: {table or 'auto-detect'}")
            console.print(f"  - Translation: {'enabled' if translate else 'disabled'}")
            console.print(f"  - Chart extraction: {'enabled' if extract_charts else 'disabled'}")
        
        # Initialize ingestion service
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            task = progress.add_task("Initializing ingestion service...", total=None)
            ingestion_service = DocumentIngestionService()
            
            progress.update(task, description="Processing document...")
            result = ingestion_service.ingest_document(
                source=source,
                force_table=table,
                translate=translate,
                extract_charts=extract_charts
            )
        
        if result.success:
            console.print("✅ [bold green]Ingestion completed successfully![/bold green]")
            console.print(f"📄 Document ID: {result.document_id}")
            console.print(f"📊 Table: {result.table_name}")
            console.print(f"📝 Chunks created: {result.chunks_created}")
            if result.processing_time:
                console.print(f"⏱️  Processing time: {result.processing_time:.2f}s")
        else:
            console.print("❌ [bold red]Ingestion failed![/bold red]")
            console.print(f"Error: {result.error_message}")
            raise typer.Exit(1)
            
    except KeyboardInterrupt:
        console.print("\n⚠️  Ingestion cancelled by user")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"❌ [bold red]Ingestion failed: {str(e)}[/bold red]")
        if verbose:
            console.print_exception()
        raise typer.Exit(1)


@app.command()
def ask(
    question: str = typer.Argument(..., help="Question to ask about the documents"),
    top_k: int = typer.Option(
        None,
        "--top-k",
        help="Number of similar chunks to retrieve"
    ),
    threshold: float = typer.Option(
        None,
        "--threshold",
        help="Minimum similarity threshold"
    ),
    stream: bool = typer.Option(
        False,
        "--stream/--no-stream",
        help="Stream the response in real-time"
    ),
    sources: bool = typer.Option(
        False,
        "--sources",
        help="Include source information in the response"
    ),
    format_output: str = typer.Option(
        "text",
        "--format",
        help="Output format: text or json"
    ),
    verbose: bool = typer.Option(
        False,
        "--verbose",
        "-v",
        help="Enable verbose output"
    )
):
    """
    Ask a question about the ingested documents and franchisors.
    
    Examples:
        docqa ask "What franchise opportunities are available?"
        docqa ask "Tell me about the refund policy" --sources
        docqa ask "What are the costs involved?" --stream --top-k 10
    """
    try:
        # Get configuration and set defaults
        config = get_config()
        if top_k is None:
            top_k = config.top_k
        if threshold is None:
            threshold = config.similarity_threshold

        if verbose:
            console.print(f"Question: [bold]{question}[/bold]")
            console.print(f"Parameters: top_k={top_k}, threshold={threshold}")

        console.print("🤔 [bold blue]Processing your question...[/bold blue]")
        
        if stream:
            # Stream response
            console.print("💭 [italic]Thinking...[/italic]\n")
            
            answer_parts = []
            for chunk in ask_question_stream(
                question=question,
                top_k=top_k,
                similarity_threshold=threshold
            ):
                console.print(chunk, end="")
                answer_parts.append(chunk)
            
            console.print("\n")  # New line after streaming
            
        else:
            # Regular response
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task("Generating answer...", total=None)
                
                answer = ask_question(
                    question=question,
                    top_k=top_k,
                    similarity_threshold=threshold,
                    include_metadata=sources,
                    format=format_output
                )
            
            # Display answer
            if format_output == "json":
                console.print_json(answer)
            else:
                # Display as markdown for better formatting
                console.print(Panel(
                    Markdown(answer),
                    title="💡 Answer",
                    border_style="blue"
                ))
        
        if verbose:
            # Show context information
            context = get_question_context(question, top_k=3)
            if 'error' not in context:
                console.print(f"\n📊 Context: {context['results_count']} results found")
                console.print(f"   - Franchisors: {len(context.get('franchisor_results', []))}")
                console.print(f"   - Documents: {len(context.get('document_results', []))}")
                
    except KeyboardInterrupt:
        console.print("\n⚠️  Question cancelled by user")
        raise typer.Exit(1)
    except Exception as e:
        console.print(f"❌ [bold red]Failed to process question: {str(e)}[/bold red]")
        if verbose:
            console.print_exception()
        raise typer.Exit(1)


@app.command()
def status():
    """
    Show the current status of the DocQA system.
    """
    try:
        console.print("🔍 [bold blue]Checking DocQA system status...[/bold blue]")
        
        health = health_check()
        
        if health['status'] == 'healthy':
            console.print("✅ [bold green]System is healthy![/bold green]")
            
            # Show configuration
            config_info = health.get('config', {})
            console.print("\n📋 [bold]Configuration:[/bold]")
            console.print(f"  - Chat Model: {config_info.get('chat_model')}")
            console.print(f"  - Embedding Model: {config_info.get('embedding_model')}")
            console.print(f"  - Top K: {config_info.get('top_k')}")
            console.print(f"  - Similarity Threshold: {config_info.get('similarity_threshold')}")
            
            # Show service status
            console.print("\n🔧 [bold]Services:[/bold]")
            console.print(f"  - Embedding Service: {health.get('embedding_service', 'unknown')}")
            console.print(f"  - Database: {health.get('database', 'unknown')}")
            
        else:
            console.print("❌ [bold red]System is unhealthy![/bold red]")
            console.print(f"Error: {health.get('error', 'Unknown error')}")
            raise typer.Exit(1)
            
    except Exception as e:
        console.print(f"❌ [bold red]Status check failed: {str(e)}[/bold red]")
        raise typer.Exit(1)


@app.command()
def interactive():
    """
    Start an interactive question-answering session.
    """
    console.print("🚀 [bold green]Welcome to DocQA Interactive Mode![/bold green]")
    console.print("Type your questions and press Enter. Type 'quit' or 'exit' to leave.\n")
    
    try:
        while True:
            question = typer.prompt("❓ Your question")
            
            if question.lower() in ['quit', 'exit', 'q']:
                console.print("👋 Goodbye!")
                break
            
            if not question.strip():
                console.print("Please enter a valid question.\n")
                continue
            
            try:
                console.print("💭 [italic]Thinking...[/italic]")
                
                answer = ask_question(question)
                
                console.print(Panel(
                    Markdown(answer),
                    title="💡 Answer",
                    border_style="blue"
                ))
                console.print()  # Empty line for spacing
                
            except Exception as e:
                console.print(f"❌ Error: {str(e)}\n")
                
    except KeyboardInterrupt:
        console.print("\n👋 Goodbye!")


def main():
    """Main entry point for the CLI"""
    try:
        app()
    except Exception as e:
        console.print(f"❌ [bold red]Fatal error: {str(e)}[/bold red]")
        sys.exit(1)


if __name__ == "__main__":
    main()
