"""
Background Processing System with Immediate Response

This module provides background document processing with:
- Immediate response to user requests
- Progress tracking and status updates
- Queue-based processing with ThreadPoolExecutor
- Task management and monitoring
- Error handling and retry logic
"""

import time
import uuid
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, Future
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
import structlog
from threading import Lock

from ..config import get_config
from ..types import IngestionResult, DocumentMetadata
from .enhanced_processor import EnhancedDocumentProcessor
from .smart_chunker import SmartChunker
from ..vector_store.bulk_operations import BulkVectorOperations

logger = structlog.get_logger()


class TaskStatus(Enum):
    """Task status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class BackgroundTask:
    """Represents a background processing task"""
    task_id: str
    source: str
    target_table: str
    document_id: Optional[str] = None
    status: TaskStatus = TaskStatus.PENDING
    progress: float = 0.0
    result: Optional[IngestionResult] = None
    error: Optional[str] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    options: Dict[str, Any] = field(default_factory=dict)
    callback: Optional[Callable] = None
    
    @property
    def processing_time(self) -> Optional[float]:
        """Calculate processing time"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        elif self.start_time:
            return time.time() - self.start_time
        return None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary for JSON serialization"""
        return {
            'task_id': self.task_id,
            'source': self.source,
            'target_table': self.target_table,
            'document_id': self.document_id,
            'status': self.status.value,
            'progress': self.progress,
            'error': self.error,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'processing_time': self.processing_time,
            'options': self.options,
            'result': self.result.to_dict() if self.result else None
        }


class BackgroundProcessor:
    """
    Background document processor with immediate response and progress tracking
    """
    
    def __init__(self, max_workers: Optional[int] = None):
        """
        Initialize background processor

        Args:
            max_workers: Maximum number of worker threads
        """
        config = get_config()
        self.max_workers = max_workers or min(8, (config.max_workers or 4))
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)

        # Store config for later use
        self.config = config
        
        # Task management
        self.tasks: Dict[str, BackgroundTask] = {}
        self.futures: Dict[str, Future] = {}
        self.task_lock = Lock()
        
        # Processing components
        self.enhanced_processor = EnhancedDocumentProcessor(max_workers=self.max_workers)
        self.smart_chunker = SmartChunker()
        self.bulk_operations = BulkVectorOperations()
        
        logger.info("Background processor initialized", 
                   max_workers=self.max_workers)
    
    def submit_document_processing(
        self,
        source: str,
        target_table: str = "documents",
        document_id: Optional[str] = None,
        force_processing: bool = False,
        extract_charts: bool = True,
        extract_tables: bool = True,
        use_ocr: bool = True,
        callback: Optional[Callable] = None
    ) -> str:
        """
        Submit document for background processing with immediate response
        
        Args:
            source: Document source (URL, S3 path, or local file)
            target_table: Target table (documents or franchisors)
            document_id: Optional document ID (generates new if None)
            force_processing: Force reprocessing even if already exists
            extract_charts: Whether to extract and analyze charts
            extract_tables: Whether to extract tables
            use_ocr: Whether to use OCR for images
            callback: Optional callback function for completion
            
        Returns:
            Task ID for tracking progress
        """
        task_id = str(uuid.uuid4())
        
        # Create task
        task = BackgroundTask(
            task_id=task_id,
            source=source,
            target_table=target_table,
            document_id=document_id or str(uuid.uuid4()),
            options={
                'force_processing': force_processing,
                'extract_charts': extract_charts,
                'extract_tables': extract_tables,
                'use_ocr': use_ocr
            },
            callback=callback
        )
        
        # Store task
        with self.task_lock:
            self.tasks[task_id] = task
        
        # Submit to executor
        future = self.executor.submit(self._process_document_task, task)
        self.futures[task_id] = future
        
        logger.info("Document processing task submitted", 
                   task_id=task_id, source=source, target_table=target_table)
        
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get task status and progress
        
        Args:
            task_id: Task identifier
            
        Returns:
            Task status dictionary or None if not found
        """
        with self.task_lock:
            task = self.tasks.get(task_id)
            if task:
                return task.to_dict()
        return None
    
    def get_all_tasks(self) -> List[Dict[str, Any]]:
        """Get status of all tasks"""
        with self.task_lock:
            return [task.to_dict() for task in self.tasks.values()]
    
    def cancel_task(self, task_id: str) -> bool:
        """
        Cancel a pending or processing task
        
        Args:
            task_id: Task identifier
            
        Returns:
            True if cancelled successfully
        """
        with self.task_lock:
            task = self.tasks.get(task_id)
            if not task:
                return False
            
            if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                return False
            
            # Cancel future if possible
            future = self.futures.get(task_id)
            if future and not future.done():
                cancelled = future.cancel()
                if cancelled:
                    task.status = TaskStatus.CANCELLED
                    task.end_time = time.time()
                    logger.info("Task cancelled", task_id=task_id)
                    return True
            
            return False
    
    def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> Optional[IngestionResult]:
        """
        Wait for task completion
        
        Args:
            task_id: Task identifier
            timeout: Maximum wait time in seconds
            
        Returns:
            IngestionResult or None if timeout/error
        """
        future = self.futures.get(task_id)
        if not future:
            return None
        
        try:
            future.result(timeout=timeout)
            
            with self.task_lock:
                task = self.tasks.get(task_id)
                if task and task.result:
                    return task.result
                    
        except Exception as e:
            logger.error("Task wait failed", task_id=task_id, error=str(e))
        
        return None
    
    def _process_document_task(self, task: BackgroundTask):
        """Process a document task in background"""
        try:
            task.status = TaskStatus.PROCESSING
            task.start_time = time.time()
            task.progress = 0.1
            
            logger.info("Starting document processing task", 
                       task_id=task.task_id, source=task.source)
            
            # Step 1: Prepare file (10% progress)
            file_path = self._prepare_file(task.source)
            if not file_path:
                raise Exception("Failed to prepare file")
            
            task.progress = 0.2
            
            # Step 2: Check for existing document (20% progress)
            if not task.options.get('force_processing', False):
                existing_doc = self.enhanced_processor.is_document_processed(file_path)
                if existing_doc:
                    task.result = IngestionResult(
                        success=True,
                        document_id=existing_doc,
                        chunks_created=0,
                        table_name=task.target_table,
                        processing_time=0.0,
                        message="Document already processed"
                    )
                    task.status = TaskStatus.COMPLETED
                    task.end_time = time.time()
                    task.progress = 1.0
                    return
            
            task.progress = 0.3
            
            # Step 3: Process document with enhanced processor (30-60% progress)
            processing_result = self.enhanced_processor.process_document_parallel(
                file_path=file_path,
                extract_charts=task.options.get('extract_charts', True),
                extract_tables=task.options.get('extract_tables', True),
                use_ocr=task.options.get('use_ocr', True)
            )
            
            if not processing_result.success:
                raise Exception(processing_result.error_message)
            
            task.progress = 0.6
            
            # Step 4: Create smart chunks (60-70% progress)
            metadata = DocumentMetadata(
                document_id=task.document_id,
                filename=file_path.name,
                file_type=file_path.suffix.lower(),
                file_size=file_path.stat().st_size,
                source_url=task.source if task.source.startswith(('http', 's3://')) else None
            )
            
            chunks = self.smart_chunker.chunk_text(
                text=processing_result.text_content,
                metadata=metadata,
                sections=processing_result.sections_detected
            )
            
            if not chunks:
                raise Exception("No content chunks created")
            
            task.progress = 0.7
            
            # Step 5: Bulk ingest with vector operations (70-90% progress)
            ingestion_result = self.bulk_operations.bulk_ingest_chunks(
                chunks=chunks,
                document_id=task.document_id,
                table_name=task.target_table,
                metadata=metadata,
                source_url=task.source
            )
            
            if not ingestion_result.success:
                raise Exception(ingestion_result.error_message)
            
            task.progress = 0.9
            
            # Step 6: Update cache and finalize (90-100% progress)
            doc_hash = self.enhanced_processor.get_document_hash(file_path)
            if doc_hash:
                self.enhanced_processor.document_cache[doc_hash] = task.document_id
            
            task.result = ingestion_result
            task.status = TaskStatus.COMPLETED
            task.progress = 1.0
            
            logger.info("Document processing task completed", 
                       task_id=task.task_id,
                       chunks_created=ingestion_result.chunks_created,
                       processing_time=task.processing_time)
            
            # Call callback if provided
            if task.callback:
                try:
                    task.callback(task.result)
                except Exception as e:
                    logger.warning("Task callback failed", 
                                 task_id=task.task_id, error=str(e))
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = str(e)
            task.progress = 0.0
            
            logger.error("Document processing task failed", 
                        task_id=task.task_id, error=str(e))
            
        finally:
            task.end_time = time.time()
            
            # Clean up temporary files
            if hasattr(self, '_temp_files'):
                for temp_file in self._temp_files:
                    try:
                        temp_file.unlink()
                    except:
                        pass
    
    def _prepare_file(self, source: str) -> Optional[Path]:
        """Prepare file for processing (download if needed)"""
        try:
            if source.startswith('s3://'):
                # Handle S3 URLs
                return self._download_s3_file(source)
            elif source.startswith('http'):
                # Handle HTTP URLs
                return self._download_http_file(source)
            else:
                # Handle local files
                file_path = Path(source)
                if file_path.exists():
                    return file_path
                else:
                    logger.error("Local file not found", path=source)
                    return None
                    
        except Exception as e:
            logger.error("File preparation failed", source=source, error=str(e))
            return None
    
    def _download_s3_file(self, s3_url: str) -> Optional[Path]:
        """Download file from S3"""
        try:
            import boto3
            from urllib.parse import urlparse
            
            parsed = urlparse(s3_url)
            bucket = parsed.netloc
            key = parsed.path.lstrip('/')
            
            s3_client = boto3.client('s3')
            
            # Create temporary file
            temp_dir = Path(self.config.temp_dir)
            temp_dir.mkdir(exist_ok=True)
            
            temp_file = temp_dir / f"temp_{uuid.uuid4()}{Path(key).suffix}"
            
            # Download file
            s3_client.download_file(bucket, key, str(temp_file))
            
            # Track for cleanup
            if not hasattr(self, '_temp_files'):
                self._temp_files = []
            self._temp_files.append(temp_file)
            
            return temp_file
            
        except Exception as e:
            logger.error("S3 download failed", s3_url=s3_url, error=str(e))
            return None
    
    def _download_http_file(self, url: str) -> Optional[Path]:
        """Download file from HTTP URL"""
        try:
            import requests
            from urllib.parse import urlparse
            
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            # Create temporary file
            temp_dir = Path(self.config.temp_dir)
            temp_dir.mkdir(exist_ok=True)
            
            # Determine file extension from URL or content type
            parsed = urlparse(url)
            file_ext = Path(parsed.path).suffix
            
            if not file_ext:
                content_type = response.headers.get('content-type', '')
                if 'pdf' in content_type:
                    file_ext = '.pdf'
                elif 'image' in content_type:
                    file_ext = '.png'
                else:
                    file_ext = '.bin'
            
            temp_file = temp_dir / f"temp_{uuid.uuid4()}{file_ext}"
            
            # Download file
            with open(temp_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            # Track for cleanup
            if not hasattr(self, '_temp_files'):
                self._temp_files = []
            self._temp_files.append(temp_file)
            
            return temp_file
            
        except Exception as e:
            logger.error("HTTP download failed", url=url, error=str(e))
            return None
    
    def shutdown(self):
        """Shutdown the background processor"""
        logger.info("Shutting down background processor")
        
        # Cancel all pending tasks
        with self.task_lock:
            for task_id in list(self.tasks.keys()):
                self.cancel_task(task_id)
        
        # Shutdown executor
        self.executor.shutdown(wait=True)
        
        # Close bulk operations
        self.bulk_operations.close()
        
        logger.info("Background processor shutdown complete")
