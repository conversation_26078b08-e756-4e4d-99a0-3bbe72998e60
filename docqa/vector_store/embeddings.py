"""
Embedding service using OpenAI text-embedding-3-small
"""

import time
from typing import List
import openai
import structlog
from tenacity import retry, stop_after_attempt, wait_exponential

from ..config import get_config
from ..types import EmbeddingError

logger = structlog.get_logger()


class EmbeddingService:
    """Service for generating embeddings using OpenAI"""
    
    def __init__(self):
        config = get_config()
        if not config:
            raise ValueError("Configuration not available. Please ensure OPENAI_API_KEY is set.")
        
        self.client = openai.OpenAI(api_key=config.openai_api_key)
        self.model = config.embedding_model
        self.dimension = 1536  # text-embedding-3-small dimension
        
        logger.info("Embedding service initialized", model=self.model)
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    def generate_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for a single text
        
        Args:
            text: Text to embed
            
        Returns:
            List of floats representing the embedding
            
        Raises:
            EmbeddingError: If embedding generation fails
        """
        try:
            if not text or not text.strip():
                raise EmbeddingError("Empty text provided for embedding")
            
            # Clean and truncate text if needed
            cleaned_text = self._clean_text(text)
            
            start_time = time.time()
            
            response = self.client.embeddings.create(
                model=self.model,
                input=cleaned_text,
                encoding_format="float"
            )
            
            processing_time = time.time() - start_time
            
            if not response.data:
                raise EmbeddingError("No embedding data returned from OpenAI")
            
            embedding = response.data[0].embedding
            
            if len(embedding) != self.dimension:
                raise EmbeddingError(f"Unexpected embedding dimension: {len(embedding)}")
            
            logger.debug("Embedding generated", 
                        text_length=len(cleaned_text),
                        processing_time=processing_time,
                        tokens_used=response.usage.total_tokens if response.usage else None)
            
            return embedding
            
        except openai.OpenAIError as e:
            logger.error("OpenAI API error during embedding", error=str(e))
            raise EmbeddingError(f"OpenAI API error: {str(e)}")
        except Exception as e:
            logger.error("Unexpected error during embedding", error=str(e))
            raise EmbeddingError(f"Embedding generation failed: {str(e)}")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    def generate_embeddings_batch(self, texts: List[str], batch_size: int = None) -> List[List[float]]:
        """
        Generate embeddings for multiple texts in optimized batches

        Args:
            texts: List of texts to embed
            batch_size: Number of texts to process in each batch (defaults to config.batch_size)

        Returns:
            List of embeddings

        Raises:
            EmbeddingError: If batch embedding generation fails
        """
        if not texts:
            return []

        # Use config batch size if not specified
        if batch_size is None:
            batch_size = getattr(config, 'batch_size', 10)

        embeddings = []
        total_start_time = time.time()

        try:
            for i in range(0, len(texts), batch_size):
                batch = texts[i:i + batch_size]
                cleaned_batch = [self._clean_text(text) for text in batch if text and text.strip()]

                if not cleaned_batch:
                    continue

                start_time = time.time()

                response = self.client.embeddings.create(
                    model=self.model,
                    input=cleaned_batch,
                    encoding_format="float"
                )

                processing_time = time.time() - start_time

                if len(response.data) != len(cleaned_batch):
                    raise EmbeddingError(f"Batch size mismatch: expected {len(cleaned_batch)}, got {len(response.data)}")

                batch_embeddings = [item.embedding for item in response.data]

                # Validate dimensions
                for j, embedding in enumerate(batch_embeddings):
                    if len(embedding) != self.dimension:
                        raise EmbeddingError(f"Unexpected embedding dimension for text {i+j}: {len(embedding)}")

                embeddings.extend(batch_embeddings)

                logger.debug("Batch embeddings generated",
                           batch_size=len(cleaned_batch),
                           batch_number=i//batch_size + 1,
                           processing_time=processing_time,
                           tokens_used=response.usage.total_tokens if response.usage else None)

                # Adaptive delay based on batch size to optimize rate limits
                if i + batch_size < len(texts):
                    delay = max(0.05, 0.1 * (batch_size / 10))  # Scale delay with batch size
                    time.sleep(delay)
            
            logger.info("All embeddings generated", 
                       total_texts=len(texts),
                       total_embeddings=len(embeddings))
            
            return embeddings
            
        except openai.OpenAIError as e:
            logger.error("OpenAI API error during batch embedding", error=str(e))
            raise EmbeddingError(f"Batch embedding failed: {str(e)}")
        except Exception as e:
            logger.error("Unexpected error during batch embedding", error=str(e))
            raise EmbeddingError(f"Batch embedding generation failed: {str(e)}")
    
    def _clean_text(self, text: str) -> str:
        """Clean and prepare text for embedding"""
        if not text:
            return ""
        
        # Remove excessive whitespace
        cleaned = " ".join(text.split())
        
        # Truncate if too long (OpenAI has token limits)
        max_chars = 8000  # Conservative limit
        if len(cleaned) > max_chars:
            cleaned = cleaned[:max_chars]
            logger.warning("Text truncated for embedding", 
                          original_length=len(text),
                          truncated_length=len(cleaned))
        
        return cleaned
    
    def get_dimension(self) -> int:
        """Get embedding dimension"""
        return self.dimension
