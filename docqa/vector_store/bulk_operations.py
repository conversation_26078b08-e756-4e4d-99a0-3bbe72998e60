"""
Bulk Vector Operations for Enhanced Performance

This module provides optimized bulk operations for pgvector including:
- Batch embedding generation and insertion
- Bulk upsert operations
- Parallel processing for large datasets
- Connection pooling for better performance
"""

import time
from typing import List
import structlog
from psycopg2.extras import execute_values
from psycopg2.pool import ThreadedConnection<PERSON>ool
import uuid

from ..config import get_config
from ..types import DocumentChunk, DocumentMetadata, IngestionResult
from ..services.enhanced_openai_service import EnhancedOpenAIService

logger = structlog.get_logger()


class BulkVectorOperations:
    """
    Optimized bulk operations for vector storage with pgvector
    """
    
    def __init__(self, max_connections: int = 20):
        """
        Initialize bulk operations with connection pooling

        Args:
            max_connections: Maximum database connections in pool
        """
        config = get_config()
        self.max_connections = max_connections
        self.openai_service = EnhancedOpenAIService()

        # Initialize connection pool
        self.connection_pool = ThreadedConnectionPool(
            minconn=1,
            maxconn=max_connections,
            dsn=config.database_url
        )
        
        logger.info("Bulk vector operations initialized", 
                   max_connections=max_connections)
    
    def bulk_ingest_chunks(
        self,
        chunks: List[DocumentChunk],
        document_id: str,
        table_name: str,
        metadata: DocumentMetadata,
        source_url: str,
        batch_size: int = 50
    ) -> IngestionResult:
        """
        Bulk ingest document chunks with optimized performance
        
        Args:
            chunks: List of document chunks
            document_id: Document identifier
            table_name: Target table (franchisors or documents)
            metadata: Document metadata
            source_url: Source URL or path
            batch_size: Number of chunks to process in each batch
            
        Returns:
            IngestionResult with processing details
        """
        start_time = time.time()
        
        try:
            logger.info("Starting bulk chunk ingestion", 
                       chunks_count=len(chunks),
                       document_id=document_id,
                       table_name=table_name)
            
            if not chunks:
                return IngestionResult(
                    success=False,
                    document_id=document_id,
                    chunks_created=0,
                    table_name=table_name,
                    error_message="No chunks to ingest"
                )
            
            # Generate embeddings in batches
            embeddings = self._generate_embeddings_bulk(chunks, batch_size)
            
            if len(embeddings) != len(chunks):
                return IngestionResult(
                    success=False,
                    document_id=document_id,
                    chunks_created=0,
                    table_name=table_name,
                    error_message="Embedding count mismatch"
                )
            
            # Bulk insert into database
            success = self._bulk_insert_chunks(
                chunks, embeddings, document_id, table_name, metadata, source_url
            )
            
            if not success:
                return IngestionResult(
                    success=False,
                    document_id=document_id,
                    chunks_created=0,
                    table_name=table_name,
                    error_message="Database insertion failed"
                )
            
            processing_time = time.time() - start_time
            
            logger.info("Bulk ingestion completed", 
                       document_id=document_id,
                       chunks_created=len(chunks),
                       processing_time=processing_time)
            
            return IngestionResult(
                success=True,
                document_id=document_id,
                chunks_created=len(chunks),
                table_name=table_name,
                processing_time=processing_time
            )
            
        except Exception as e:
            logger.error("Bulk ingestion failed", 
                        document_id=document_id, error=str(e))
            return IngestionResult(
                success=False,
                document_id=document_id,
                chunks_created=0,
                table_name=table_name,
                error_message=str(e)
            )
    
    def _generate_embeddings_bulk(
        self,
        chunks: List[DocumentChunk],
        batch_size: int
    ) -> List[List[float]]:
        """Generate embeddings for chunks in optimized batches"""
        try:
            logger.info("Generating bulk embeddings", count=len(chunks))
            
            # Extract text from chunks
            texts = [chunk.text for chunk in chunks]
            
            # Use the enhanced OpenAI service for batch processing
            embeddings = self.openai_service.generate_embeddings_batch(texts)
            
            logger.info("Bulk embedding generation completed", 
                       embeddings_count=len(embeddings))
            
            return embeddings
            
        except Exception as e:
            logger.error("Bulk embedding generation failed", error=str(e))
            raise
    
    def _bulk_insert_chunks(
        self,
        chunks: List[DocumentChunk],
        embeddings: List[List[float]],
        document_id: str,
        table_name: str,
        metadata: DocumentMetadata,
        source_url: str
    ) -> bool:
        """Bulk insert chunks into database with optimized queries"""
        conn = None
        try:
            conn = self.connection_pool.getconn()
            
            with conn.cursor() as cur:
                # Start transaction
                cur.execute("BEGIN")
                
                if table_name == "documents":
                    success = self._bulk_insert_document_chunks(
                        cur, chunks, embeddings, document_id, metadata, source_url
                    )
                elif table_name == "franchisors":
                    success = self._bulk_insert_franchisor_chunks(
                        cur, chunks, embeddings, document_id, metadata, source_url
                    )
                else:
                    raise ValueError(f"Unknown table name: {table_name}")
                
                if success:
                    cur.execute("COMMIT")
                    logger.info("Bulk insert committed", 
                               table_name=table_name,
                               chunks_count=len(chunks))
                else:
                    cur.execute("ROLLBACK")
                    logger.error("Bulk insert failed, rolled back")
                
                return success
                
        except Exception as e:
            if conn:
                try:
                    with conn.cursor() as cur:
                        cur.execute("ROLLBACK")
                except:
                    pass
            logger.error("Bulk insert failed", error=str(e))
            return False
            
        finally:
            if conn:
                self.connection_pool.putconn(conn)
    
    def _bulk_insert_document_chunks(
        self,
        cursor,
        chunks: List[DocumentChunk],
        embeddings: List[List[float]],
        document_id: str,
        metadata: DocumentMetadata,
        source_url: str
    ) -> bool:
        """Bulk insert document chunks"""
        try:
            # First, delete existing chunks for this document
            cursor.execute(
                "DELETE FROM document_chunks WHERE document_id = %s",
                (document_id,)
            )
            
            # Prepare data for bulk insert
            chunk_data = []
            for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                chunk_data.append((
                    str(uuid.uuid4()),  # chunk_id
                    document_id,
                    chunk.text,
                    embedding,
                    i,  # chunk_index
                    chunk.token_count,
                    chunk.metadata or {}
                ))
            
            # Bulk insert chunks
            insert_query = """
                INSERT INTO document_chunks 
                (id, document_id, content, embedding, chunk_index, token_count, metadata)
                VALUES %s
            """
            
            execute_values(
                cursor,
                insert_query,
                chunk_data,
                template=None,
                page_size=100
            )
            
            logger.debug("Document chunks inserted", count=len(chunk_data))
            return True
            
        except Exception as e:
            logger.error("Document chunk insertion failed", error=str(e))
            return False
    
    def _bulk_insert_franchisor_chunks(
        self,
        cursor,
        chunks: List[DocumentChunk],
        embeddings: List[List[float]],
        document_id: str,
        metadata: DocumentMetadata,
        source_url: str
    ) -> bool:
        """Bulk insert franchisor chunks"""
        try:
            # For franchisors, we update the main franchisor record with combined embedding
            # and also store individual chunks for detailed retrieval
            
            # Combine all embeddings into a single representative embedding
            # (simple average - could be improved with weighted averaging)
            if embeddings:
                combined_embedding = [
                    sum(emb[i] for emb in embeddings) / len(embeddings)
                    for i in range(len(embeddings[0]))
                ]
                
                # Update franchisor with combined embedding
                cursor.execute("""
                    UPDATE franchisors 
                    SET embedding = %s, updated_at = NOW()
                    WHERE id = %s
                """, (combined_embedding, document_id))
                
                if cursor.rowcount == 0:
                    logger.warning("Franchisor not found for embedding update", 
                                 franchisor_id=document_id)
                    return False
            
            # Also store individual chunks for detailed retrieval
            # First, delete existing chunks
            cursor.execute(
                "DELETE FROM franchisor_chunks WHERE franchisor_id = %s",
                (document_id,)
            )
            
            # Prepare chunk data
            chunk_data = []
            for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
                chunk_data.append((
                    str(uuid.uuid4()),  # chunk_id
                    document_id,  # franchisor_id
                    chunk.text,
                    embedding,
                    i,  # chunk_index
                    chunk.token_count,
                    chunk.metadata or {}
                ))
            
            # Create franchisor_chunks table if it doesn't exist
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS franchisor_chunks (
                    id UUID PRIMARY KEY,
                    franchisor_id UUID NOT NULL,
                    content TEXT NOT NULL,
                    embedding vector(1536),
                    chunk_index INTEGER NOT NULL,
                    token_count INTEGER NOT NULL,
                    metadata JSONB DEFAULT '{}',
                    created_at TIMESTAMP DEFAULT NOW(),
                    FOREIGN KEY (franchisor_id) REFERENCES franchisors(id) ON DELETE CASCADE
                )
            """)
            
            # Create index if it doesn't exist
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_franchisor_chunks_embedding 
                ON franchisor_chunks USING ivfflat (embedding vector_cosine_ops)
            """)
            
            # Bulk insert chunks
            insert_query = """
                INSERT INTO franchisor_chunks 
                (id, franchisor_id, content, embedding, chunk_index, token_count, metadata)
                VALUES %s
            """
            
            execute_values(
                cursor,
                insert_query,
                chunk_data,
                template=None,
                page_size=100
            )
            
            logger.debug("Franchisor chunks inserted", count=len(chunk_data))
            return True
            
        except Exception as e:
            logger.error("Franchisor chunk insertion failed", error=str(e))
            return False
    
    def bulk_delete_document(self, document_id: str, table_name: str) -> bool:
        """Bulk delete document and all its chunks"""
        conn = None
        try:
            conn = self.connection_pool.getconn()
            
            with conn.cursor() as cur:
                cur.execute("BEGIN")
                
                if table_name == "documents":
                    # Delete document chunks
                    cur.execute(
                        "DELETE FROM document_chunks WHERE document_id = %s",
                        (document_id,)
                    )
                elif table_name == "franchisors":
                    # Delete franchisor chunks and clear embedding
                    cur.execute(
                        "DELETE FROM franchisor_chunks WHERE franchisor_id = %s",
                        (document_id,)
                    )
                    cur.execute(
                        "UPDATE franchisors SET embedding = NULL WHERE id = %s",
                        (document_id,)
                    )
                
                cur.execute("COMMIT")
                logger.info("Bulk delete completed", 
                           document_id=document_id, table_name=table_name)
                return True
                
        except Exception as e:
            if conn:
                try:
                    with conn.cursor() as cur:
                        cur.execute("ROLLBACK")
                except:
                    pass
            logger.error("Bulk delete failed", 
                        document_id=document_id, error=str(e))
            return False
            
        finally:
            if conn:
                self.connection_pool.putconn(conn)
    
    def close(self):
        """Close connection pool"""
        if hasattr(self, 'connection_pool'):
            self.connection_pool.closeall()
            logger.info("Connection pool closed")
