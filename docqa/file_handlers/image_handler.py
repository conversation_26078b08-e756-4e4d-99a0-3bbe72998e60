"""
Image file handler with OCR capabilities
"""

from pathlib import Path
from typing import Dict, Any, Optional
import structlog

try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    np = None

try:
    import pytesseract
    from PIL import Image
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False

from .base_handler import BaseFileHandler
from ..types import FileProcessingResult
from ..config import get_config

logger = structlog.get_logger()


class ImageHandler(BaseFileHandler):
    """Handler for image files with OCR capabilities"""
    
    def __init__(self):
        super().__init__()
        self.supported_extensions = ['.jpg', '.jpeg', '.png']
        
        if not OCR_AVAILABLE:
            logger.warning("OCR libraries not available, image text extraction will be limited")
        
        if not CV2_AVAILABLE:
            logger.warning("OpenCV not available, image preprocessing will be limited")
    
    def can_handle(self, file_path: Path) -> bool:
        """Check if this handler can process the file"""
        return file_path.suffix.lower() in self.supported_extensions
    
    def process_file(self, file_path: Path) -> FileProcessingResult:
        """Process image file and extract text using OCR"""
        if not self.validate_file(file_path):
            return FileProcessingResult(
                success=False,
                text_content="",
                error_message="File validation failed"
            )
        
        try:
            logger.info("Processing image file", path=str(file_path))
            
            # Load image
            image = self._load_image(file_path)
            if image is None:
                return FileProcessingResult(
                    success=False,
                    text_content="",
                    error_message="Failed to load image"
                )
            
            # Preprocess image for better OCR
            processed_image = self._preprocess_image(image)
            
            # Extract text using OCR
            text_content = self._extract_text_ocr(processed_image)
            
            # Analyze image for charts/diagrams
            chart_info = self._analyze_image_content(image, file_path)
            
            # Get image metadata
            image_metadata = self._get_image_metadata(image, file_path)
            
            # Clean extracted text
            cleaned_text = self.clean_text(text_content)
            
            # If no text found, create descriptive text from image analysis
            if not cleaned_text and chart_info:
                cleaned_text = f"Image content: {chart_info.get('description', 'Visual content detected')}"
            
            if not cleaned_text:
                cleaned_text = f"Image file: {file_path.name} (no text content extracted)"
            
            logger.info("Image processing completed",
                       path=str(file_path),
                       text_length=len(cleaned_text),
                       has_chart_info=bool(chart_info))
            
            return FileProcessingResult(
                success=True,
                text_content=cleaned_text,
                images_extracted=[{
                    'filename': file_path.name,
                    'metadata': image_metadata,
                    'chart_info': chart_info
                }]
            )
            
        except Exception as e:
            logger.error("Image processing failed", 
                        path=str(file_path),
                        error=str(e))
            return FileProcessingResult(
                success=False,
                text_content="",
                error_message=f"Image processing failed: {str(e)}"
            )
    
    def _load_image(self, file_path: Path) -> Optional[Any]:
        """Load image using OpenCV or PIL"""
        try:
            if CV2_AVAILABLE:
                image = cv2.imread(str(file_path))
                if image is not None:
                    return image
            
            # Fallback to PIL
            pil_image = Image.open(str(file_path))
            return np.array(pil_image)
            
        except Exception as e:
            logger.error("Failed to load image", path=str(file_path), error=str(e))
            return None
    
    def _preprocess_image(self, image: Any) -> Any:
        """Preprocess image for better OCR results"""
        if not CV2_AVAILABLE:
            return image
        
        try:
            # Convert to grayscale
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image
            
            # Apply denoising
            denoised = cv2.fastNlMeansDenoising(gray)
            
            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
            
            # Morphological operations to clean up
            kernel = np.ones((1, 1), np.uint8)
            processed = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
            
            return processed
            
        except Exception as e:
            logger.warning("Image preprocessing failed, using original", error=str(e))
            return image
    
    def _extract_text_ocr(self, image: Any) -> str:
        """Extract text from image using OCR"""
        if not OCR_AVAILABLE:
            logger.warning("OCR not available, cannot extract text from image")
            return ""

        try:
            # Get configuration
            config = get_config()
            # Convert numpy array to PIL Image if needed
            if isinstance(image, np.ndarray):
                # Handle different image formats
                if len(image.shape) == 3 and image.shape[2] == 3:
                    # BGR to RGB conversion for OpenCV images
                    if CV2_AVAILABLE:
                        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                        pil_image = Image.fromarray(image_rgb)
                    else:
                        pil_image = Image.fromarray(image)
                else:
                    pil_image = Image.fromarray(image)
            else:
                pil_image = image

            # Try multiple OCR configurations for better results
            ocr_configs = [
                r'--oem 3 --psm 6',  # Default: uniform block of text
                r'--oem 3 --psm 3',  # Fully automatic page segmentation
                r'--oem 3 --psm 1',  # Automatic page segmentation with OSD
                r'--oem 3 --psm 4',  # Single column of text
                r'--oem 3 --psm 8',  # Single word
            ]

            best_text = ""
            best_confidence = 0

            for config_str in ocr_configs:
                try:
                    if hasattr(config, 'ocr_languages'):
                        config_str += f' -l {config.ocr_languages}'

                    # Extract text
                    text = pytesseract.image_to_string(pil_image, config=config_str)

                    # Get confidence score if available
                    try:
                        data = pytesseract.image_to_data(pil_image, config=config_str, output_type=pytesseract.Output.DICT)
                        confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
                        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
                    except:
                        avg_confidence = len(text.strip())  # Use text length as proxy

                    # Keep the best result
                    if avg_confidence > best_confidence and text.strip():
                        best_text = text.strip()
                        best_confidence = avg_confidence

                except Exception as e:
                    logger.debug(f"OCR config failed: {config_str}, error: {e}")
                    continue

            logger.debug("OCR extraction completed",
                        text_length=len(best_text),
                        confidence=best_confidence)

            return best_text

        except Exception as e:
            logger.error("OCR text extraction failed", error=str(e))
            return ""
    
    def _analyze_image_content(self, image: Any, file_path: Path) -> Dict[str, Any]:
        """Analyze image content for charts, diagrams, etc."""
        analysis = {}
        
        try:
            # Basic image properties
            height, width = image.shape[:2]
            analysis['dimensions'] = {'width': width, 'height': height}
            
            # Simple heuristics for content type detection
            if CV2_AVAILABLE:
                # Convert to grayscale for analysis
                if len(image.shape) == 3:
                    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                else:
                    gray = image
                
                # Detect edges (might indicate charts/diagrams)
                edges = cv2.Canny(gray, 50, 150)
                edge_density = np.sum(edges > 0) / (width * height)
                
                # Detect contours (shapes in charts)
                contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                
                analysis['edge_density'] = edge_density
                analysis['contour_count'] = len(contours)
                
                # Simple classification
                if edge_density > 0.1 and len(contours) > 10:
                    analysis['likely_content'] = 'chart_or_diagram'
                    analysis['description'] = 'Image appears to contain charts or diagrams'
                elif edge_density > 0.05:
                    analysis['likely_content'] = 'structured_content'
                    analysis['description'] = 'Image contains structured visual content'
                else:
                    analysis['likely_content'] = 'text_or_photo'
                    analysis['description'] = 'Image appears to contain text or photographic content'
            
            # File-based heuristics
            file_size = file_path.stat().st_size
            analysis['file_size'] = file_size
            
            # Aspect ratio analysis
            aspect_ratio = width / height
            if 0.8 <= aspect_ratio <= 1.2:
                analysis['aspect_ratio_type'] = 'square'
            elif aspect_ratio > 1.5:
                analysis['aspect_ratio_type'] = 'landscape'
            else:
                analysis['aspect_ratio_type'] = 'portrait'
            
        except Exception as e:
            logger.warning("Image content analysis failed", error=str(e))
            analysis['error'] = str(e)
        
        return analysis
    
    def _get_image_metadata(self, image: Any, file_path: Path) -> Dict[str, Any]:
        """Get image metadata"""
        metadata = {
            'filename': file_path.name,
            'file_size': file_path.stat().st_size,
            'format': file_path.suffix.lower()
        }
        
        try:
            if len(image.shape) >= 2:
                metadata['height'] = image.shape[0]
                metadata['width'] = image.shape[1]
                metadata['channels'] = image.shape[2] if len(image.shape) == 3 else 1
            
            # Try to get additional metadata using PIL
            try:
                pil_image = Image.open(str(file_path))
                if hasattr(pil_image, '_getexif') and pil_image._getexif():
                    metadata['has_exif'] = True
                else:
                    metadata['has_exif'] = False
            except:
                metadata['has_exif'] = False
                
        except Exception as e:
            logger.warning("Failed to extract image metadata", error=str(e))
        
        return metadata
