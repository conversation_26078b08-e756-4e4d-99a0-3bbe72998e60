# 🚀 GrowthHive Startup Guide

## Quick Start Commands

### 🟢 Start Everything
```bash
./start_complete.sh
```

### 🔴 Stop Everything
```bash
./stop_growthhive.sh
```

## What the Startup Script Does

The `start_complete.sh` script handles **everything** automatically:

### ✅ Prerequisites Check
- Verifies Docker is running
- Checks for `.env` file existence
- Loads environment variables from `.env`

### ✅ Environment Setup
- Creates `venv311` virtual environment if missing
- Activates virtual environment
- Installs/updates all Python dependencies
- Installs additional LangGraph packages

### ✅ Database Setup
- Tests PostgreSQL connection using `.env` DATABASE_URL
- Runs Alembic migrations (`alembic upgrade head`)
- Sets up pgvector for DocQA if needed

### ✅ Directory Creation
- Creates `logs/`, `data/`, `uploads/` directories

### ✅ Background Services
- Stops any existing Docker containers
- Starts RabbitMQ and Redis via Docker Compose
- Waits for services to be fully ready
- Starts Celery worker in background

### ✅ Application Startup
- Verifies application can import successfully
- Starts FastAPI server on http://localhost:8000

## Prerequisites

Before running the startup script, ensure you have:

1. **Docker Desktop** running
2. **PostgreSQL** installed and running locally
3. **`.env` file** configured (see below)
4. **Python 3.10+** installed

## Environment Configuration

Your `.env` file must contain at minimum:

```env
# Database Configuration (Local PostgreSQL)
DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/growthhive

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# RabbitMQ Configuration (Docker services)
CELERY_BROKER_URL=amqp://growthhive:growthhive123@localhost:5672//
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Redis Configuration (Docker service)
REDIS_URL=redis://localhost:6379/0

# JWT Configuration
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production-32-chars
```

## Service URLs After Startup

- **Main Application**: http://localhost:8000
- **API Documentation**: http://localhost:8000/api/docs
- **Interactive API**: http://localhost:8000/api/redoc
- **RabbitMQ Management**: http://localhost:15672 (growthhive/growthhive123)
- **Redis Commander**: http://localhost:8081 (admin/growthhive123)

## Log Files

- **Celery Worker**: `logs/celery_worker.log`
- **Application**: Displayed in terminal

## Troubleshooting

### Database Connection Issues
```bash
# Check if PostgreSQL is running
sudo service postgresql status  # Linux
brew services list | grep postgresql  # macOS

# Test database connection manually
psql -h localhost -U your_username -d growthhive
```

### Docker Issues
```bash
# Check Docker status
docker info

# Check running containers
docker ps

# View container logs
docker logs growthhive-rabbitmq
docker logs growthhive-redis
```

### Port Conflicts
Make sure these ports are available:
- **8000**: FastAPI application
- **5672**: RabbitMQ AMQP
- **15672**: RabbitMQ Management UI
- **6379**: Redis
- **8081**: Redis Commander

### Process Issues
```bash
# Check running processes
ps aux | grep -E "(celery|uvicorn|python)"

# Kill specific processes if needed
pkill -f "celery_worker.py"
pkill -f "uvicorn"
```

## Manual Commands (if needed)

### Start Individual Services
```bash
# Start only Docker services
docker-compose -f docker-compose.rabbitmq.yml up -d rabbitmq redis

# Start only Celery worker
source venv311/bin/activate
python celery_worker.py --loglevel=info --concurrency=4 --queues=document_processing

# Start only FastAPI server
source venv311/bin/activate
python start_server.py
```

### Database Operations
```bash
# Run migrations manually
source venv311/bin/activate
alembic upgrade head

# Setup pgvector manually
source venv311/bin/activate
python setup_docqa.py
```

## Development Tips

1. **Environment Variables**: Always use `.env` file for configuration
2. **Virtual Environment**: The script expects `venv311` directory
3. **PostgreSQL**: Must be running locally (not in Docker)
4. **Logs**: Check `logs/celery_worker.log` for background task issues
5. **API Testing**: Use http://localhost:8000/api/docs for interactive testing

## Script Features

- **Automatic Recovery**: Handles missing virtual environment
- **Service Health Checks**: Waits for services to be fully ready
- **Graceful Shutdown**: Ctrl+C stops all services cleanly
- **Error Handling**: Clear error messages with troubleshooting hints
- **Status Updates**: Colored output shows progress at each step
- **Environment Validation**: Checks all prerequisites before starting

## Next Steps

After successful startup:
1. Visit http://localhost:8000/api/docs to explore the API
2. Check service health at http://localhost:8000/health
3. Monitor background tasks at http://localhost:15672 (RabbitMQ)
4. View logs in `logs/celery_worker.log` for task processing
