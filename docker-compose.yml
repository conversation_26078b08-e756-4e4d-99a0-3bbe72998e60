version: "3"

services:
  backend:
    container_name: growthhive-backend
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    env_file:
      - .env
    environment:
      - TZ=Asia/Kolkata
      - RABBITMQ_HOST=rabbitmq
      - REDIS_HOST=redis
    depends_on:
      - rabbitmq
      - redis
    volumes:
      - .:/app
    networks:
      - growthhive_network
    restart: always
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --env-file .env

  celery:
    container_name: growthhive-celery
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - .env.staging
    depends_on:
      - backend
      - rabbitmq
      - redis
    working_dir: /app
    environment:
      - PYTHONPATH=/app
      - RABBITMQ_HOST=rabbitmq
      - REDIS_HOST=redis
    command: python celery_worker.py
    volumes:
      - .:/app
    networks:
      - growthhive_network
    restart: always

  rabbitmq:
    container_name: rabbitmq
    image: rabbitmq:3.12-management
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=guest
      - RABBITMQ_DEFAULT_PASS=guest
    networks:
      - growthhive_network
    restart: always

  redis:
    container_name: redis
    image: redis:6.2
    ports:
      - "6379:6379"
    networks:
      - growthhive_network
    restart: always

networks:
  growthhive_network:
    driver: bridge