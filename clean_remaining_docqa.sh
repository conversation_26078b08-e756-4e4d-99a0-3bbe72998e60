#!/bin/bash

echo "🧹 Cleaning Remaining DocQA References"
echo "======================================"

# Remove DocQA processing tasks
echo "🗑️  Removing DocQA processing tasks..."
rm -f app/tasks/docqa_processing.py
rm -f app/tasks/document_processing.py

# Remove DocQA-related files
echo "🗑️  Removing DocQA-related files..."
rm -f docqa.py
rm -f setup_docqa.py
rm -f direct_content_analysis.py
rm -f url_demo_simple.py
rm -f test_docqa_integration.py
rm -f test_file_handlers_comprehensive.py
rm -f test_file_handlers_integration.py
rm -f create_s3_test_files.py

# Remove DocQA documentation
echo "🗑️  Removing DocQA documentation..."
rm -f CURRENT_RAG_SYSTEM.md
rm -f BACKGROUND_BROCHURE_PROCESSING.md
rm -f WEBHOOK_RAG_INTEGRATION_COMPLETE.md

# Clean up Docker compose file
echo "🗑️  Cleaning Docker compose file..."
sed -i '' '/docqa-monitor:/,/container_name: growthhive-docqa-monitor/d' docker-compose.rabbitmq.yml
sed -i '' '/command: python -m docqa.monitoring.status_tracker/d' docker-compose.rabbitmq.yml

# Clean up scripts
echo "🗑️  Cleaning scripts..."
rm -f scripts/clear_document_data.py

# Clean up Alembic migrations
echo "🗑️  Cleaning Alembic migrations..."
rm -f alembic/versions/003_add_pgvector_support_for_docqa.py
rm -f alembic/versions/004_add_document_processing_status.py

# Clean up app directory DocQA references
echo "🗑️  Cleaning app directory DocQA references..."
find app/ -name "*.py" -type f -exec sed -i '' '/from docqa/d' {} \;
find app/ -name "*.py" -type f -exec sed -i '' '/import docqa/d' {} \;
find app/ -name "*.py" -type f -exec sed -i '' '/DocQA/d' {} \;
find app/ -name "*.py" -type f -exec sed -i '' '/docqa_processing/d' {} \;
find app/ -name "*.py" -type f -exec sed -i '' '/process_docqa_task/d' {} \;

# Clean up any remaining imports
echo "🔍 Cleaning up remaining imports..."
find . -name "*.py" -type f -exec sed -i '' '/from docqa/d' {} \;
find . -name "*.py" -type f -exec sed -i '' '/import docqa/d' {} \;
find . -name "*.py" -type f -exec sed -i '' '/from ingest/d' {} \;
find . -name "*.py" -type f -exec sed -i '' '/import ingest/d' {} \;

# Remove empty task files
echo "🗑️  Removing empty task files..."
if [ -f app/tasks/docqa_processing.py ] && [ ! -s app/tasks/docqa_processing.py ]; then
    rm -f app/tasks/docqa_processing.py
fi

if [ -f app/tasks/document_processing.py ] && [ ! -s app/tasks/document_processing.py ]; then
    rm -f app/tasks/document_processing.py
fi

# Clean up any empty directories
echo "🧹 Cleaning up empty directories..."
find . -type d -empty -delete

echo "✅ Remaining DocQA references cleaned!"
echo ""
echo "📊 Summary:"
echo "- Removed DocQA processing tasks"
echo "- Cleaned up Docker compose configuration"
echo "- Removed DocQA-related scripts and tests"
echo "- Cleaned up Alembic migrations"
echo "- Removed all DocQA imports from app directory"
echo ""
echo "🎯 Your project now has:"
echo "- No DocQA dependencies"
echo "- No document processing tasks"
echo "- Clean Celery worker configuration"
echo "- Focused on core FastAPI functionality" 