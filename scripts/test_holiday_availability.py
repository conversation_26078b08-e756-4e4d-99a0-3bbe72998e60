#!/usr/bin/env python3
"""
Test Holiday Availability Service
Tests the holiday availability checking functionality
"""

import asyncio
import sys
import os
from datetime import datetime, date, time

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncpg
from app.services.holiday_availability_service import HolidayAvailabilityService


class MockDB:
    """Mock database session for testing"""
    
    def __init__(self, conn):
        self.conn = conn
    
    async def execute(self, query):
        """Execute query and return mock result"""
        return MockResult([])
    
    async def commit(self):
        """Mock commit"""
        pass


class MockResult:
    """Mock query result"""
    
    def __init__(self, data):
        self.data = data
    
    def scalars(self):
        return MockScalars(self.data)
    
    def scalar_one_or_none(self):
        return self.data[0] if self.data else None


class MockScalars:
    """Mock scalars result"""
    
    def __init__(self, data):
        self.data = data
    
    def all(self):
        return self.data


async def test_holiday_availability():
    """Test holiday availability functionality"""
    print("🏖️  Testing Holiday Availability Service")
    print("="*50)
    
    # Database connection parameters
    DATABASE_URL = "postgresql://postgres:root@localhost:5432/growthhive"
    
    try:
        # Connect to database
        conn = await asyncpg.connect(DATABASE_URL)
        print("✅ Connected to database")
        
        # Create mock database session
        mock_db = MockDB(conn)
        
        # Initialize availability service
        availability_service = HolidayAvailabilityService(mock_db)
        
        # Test current availability
        print("\n📅 Testing current availability...")
        is_available, message = await availability_service.check_admin_availability()
        
        print(f"Is Available: {is_available}")
        if message:
            print(f"Message: {message}")
        
        # Test specific datetime
        print("\n📅 Testing specific datetime...")
        test_datetime = datetime(2024, 12, 25, 10, 30)  # Christmas Day
        is_available, message = await availability_service.check_admin_availability(test_datetime)
        
        print(f"Test DateTime: {test_datetime}")
        print(f"Is Available: {is_available}")
        if message:
            print(f"Message: {message}")
        
        # Test current holidays
        print("\n📅 Getting current holidays...")
        current_holidays = await availability_service.get_current_holidays()
        print(f"Current Holidays: {current_holidays}")
        
        # Test date range availability
        print("\n📅 Testing date range availability...")
        start_date = date(2024, 12, 20)
        end_date = date(2024, 12, 30)
        range_availability = await availability_service.is_admin_available_for_date_range(start_date, end_date)
        print(f"Date Range Availability: {range_availability}")
        
        await conn.close()
        print("\n✅ Holiday availability testing completed!")
        
    except Exception as e:
        print(f"❌ Error: {e}")


async def test_real_database_queries():
    """Test with real database queries"""
    print("\n🔍 Testing with real database queries...")
    
    DATABASE_URL = "postgresql://postgres:root@localhost:5432/growthhive"
    
    try:
        conn = await asyncpg.connect(DATABASE_URL)
        
        # Check if holiday table exists and has data
        holidays = await conn.fetch("""
            SELECT 
                id, holiday_type, date, all_day, start_time, end_time, description, is_active
            FROM holiday 
            WHERE is_active = true AND is_deleted = false
            ORDER BY date
            LIMIT 10;
        """)
        
        print(f"Found {len(holidays)} active holidays:")
        for holiday in holidays:
            print(f"  - {holiday['date']} ({holiday['holiday_type']}): {holiday['description']}")
            if not holiday['all_day']:
                print(f"    Time: {holiday['start_time']} - {holiday['end_time']}")
        
        # Check if general_message table has out_of_reach message
        out_of_reach_msg = await conn.fetchrow("""
            SELECT id, message, message_type
            FROM general_messages 
            WHERE message_type = 'out_of_reach'
            LIMIT 1;
        """)
        
        if out_of_reach_msg:
            print(f"\nOut of reach message found:")
            print(f"  Message: {out_of_reach_msg['message']}")
        else:
            print(f"\n❌ No 'out_of_reach' message found in general_messages table")
            
            # Insert a sample out_of_reach message
            print("🔨 Creating sample out_of_reach message...")
            await conn.execute("""
                INSERT INTO general_messages (message, message_type) 
                VALUES ($1, $2)
                ON CONFLICT DO NOTHING;
            """, 
            "Thank you for your interest in our franchise opportunities. Our team is currently unavailable due to holidays. We will get back to you as soon as possible. Have a great day!",
            "out_of_reach"
            )
            print("✅ Sample out_of_reach message created")
        
        await conn.close()
        
    except Exception as e:
        print(f"❌ Database query error: {e}")


async def main():
    """Main test function"""
    await test_real_database_queries()
    await test_holiday_availability()


if __name__ == "__main__":
    asyncio.run(main())
