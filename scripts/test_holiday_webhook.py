#!/usr/bin/env python3
"""
Test Holiday Availability in Webhook Flow
Tests the holiday availability checking in the SMS webhook flow
"""

import requests
import json
from datetime import datetime


def test_webhook_with_holiday_check():
    """Test webhook with holiday availability check"""
    print("🏖️  Testing Holiday Availability in SMS Webhook")
    print("="*60)
    
    # Webhook URL
    url = "http://localhost:8000/api/webhooks/webhooks/kudosity"
    
    # Test scenarios
    test_scenarios = [
        {
            "name": "Normal Day - Should Work",
            "description": "Test on a normal day when admin should be available",
            "message": "Hello, I'm interested in franchise opportunities"
        },
        {
            "name": "Holiday Check - Christmas Day",
            "description": "Test during a holiday when admin should be unavailable",
            "message": "Hi, can you tell me about your franchises?"
        },
        {
            "name": "Follow-up Message",
            "description": "Test follow-up message to see if conversation continues or terminates",
            "message": "Are you there? I have more questions."
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n--- Test {i}: {scenario['name']} ---")
        print(f"Description: {scenario['description']}")
        print(f"👤 User Message: {scenario['message']}")
        
        # Prepare webhook payload (Kudosity format)
        payload = {
            "event_type": "SMS_INBOUND",
            "timestamp": datetime.now().isoformat() + "Z",
            "mo": {
                "type": "SMS",
                "id": f"holiday_test_{i}_{int(datetime.now().timestamp())}",
                "message": scenario['message'],
                "recipient": "1234567890",  # Your business number
                "sender": "1234567890",     # Test sender number
                "routed_via": "1234567890"
            }
        }
        
        try:
            # Send webhook request
            print("📤 Sending webhook request...")
            response = requests.post(
                url, 
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            print(f"📥 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    print("✅ Webhook processed successfully")
                    
                    # Check if response indicates admin unavailability
                    if response_data.get("success"):
                        print("🤖 Bot Response: Available and processing")
                    else:
                        print("🏖️ Bot Response: May indicate unavailability")
                        
                except json.JSONDecodeError:
                    print("📄 Response (text):", response.text[:200])
                    
            else:
                print(f"❌ Webhook failed with status {response.status_code}")
                print("📄 Response:", response.text[:200])
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
        
        print("-" * 60)
    
    print("\n📋 Test Summary:")
    print("1. Check the console output of your FastAPI server")
    print("2. Look for holiday availability check messages")
    print("3. Verify that out_of_reach messages are sent when admin is unavailable")
    print("4. Confirm that conversations are terminated during holidays")


def test_availability_api_endpoints():
    """Test availability-related API endpoints if they exist"""
    print("\n🔍 Testing Availability API Endpoints")
    print("="*50)
    
    base_url = "http://localhost:8000"
    
    # Test endpoints that might exist
    endpoints_to_test = [
        "/api/holidays",
        "/api/general-messages/out_of_reach",
        "/api/availability/check"
    ]
    
    for endpoint in endpoints_to_test:
        url = base_url + endpoint
        print(f"\n📡 Testing: {url}")
        
        try:
            response = requests.get(url, timeout=10)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ Success: {len(str(data))} characters of data")
                except:
                    print(f"✅ Success: {len(response.text)} characters of text")
            elif response.status_code == 401:
                print("🔐 Requires authentication")
            elif response.status_code == 404:
                print("❌ Endpoint not found")
            else:
                print(f"⚠️  Status {response.status_code}: {response.text[:100]}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")


def show_instructions():
    """Show instructions for testing"""
    print("\n📖 Testing Instructions:")
    print("="*50)
    print("1. Make sure your FastAPI server is running:")
    print("   uvicorn app.main:app --reload")
    print("")
    print("2. Ensure you have holiday data in your database:")
    print("   python scripts/simple_holiday_table_check.py")
    print("")
    print("3. Make sure you have an 'out_of_reach' message in general_messages table")
    print("")
    print("4. To test during actual holidays, add a holiday for today's date:")
    print("   INSERT INTO holiday (holiday_type, date, all_day, description)")
    print("   VALUES ('PREDEFINED', CURRENT_DATE, true, 'Test Holiday');")
    print("")
    print("5. Run this test script and observe the behavior")
    print("")
    print("6. Check the FastAPI console for detailed logs")


if __name__ == "__main__":
    show_instructions()
    
    # Ask user if they want to proceed
    response = input("\nDo you want to run the webhook tests? (y/n): ").lower().strip()
    
    if response in ['y', 'yes']:
        test_webhook_with_holiday_check()
        test_availability_api_endpoints()
    else:
        print("Test cancelled. Make sure to set up the environment first!")
