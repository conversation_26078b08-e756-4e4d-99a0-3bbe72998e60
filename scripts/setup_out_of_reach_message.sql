-- Setup out_of_reach message in general_messages table
-- This script ensures the out_of_reach message exists for holiday availability functionality

-- Create general_messages table if it doesn't exist
CREATE TABLE IF NOT EXISTS general_messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    message TEXT,
    message_type VARCHAR(50)
);

-- Insert or update the out_of_reach message
INSERT INTO general_messages (message, message_type) 
VALUES (
    'Thank you for your interest in our franchise opportunities. Our team is currently unavailable due to holidays. We will get back to you as soon as possible. Have a great day!',
    'out_of_reach'
)
ON CONFLICT (message_type) DO UPDATE SET
    message = EXCLUDED.message
WHERE general_messages.message_type = 'out_of_reach';

-- If the above doesn't work due to no unique constraint, try this approach:
-- First delete any existing out_of_reach messages
DELETE FROM general_messages WHERE message_type = 'out_of_reach';

-- Then insert the new one
INSERT INTO general_messages (message, message_type) 
VALUES (
    'Thank you for your interest in our franchise opportunities. Our team is currently unavailable due to holidays. We will get back to you as soon as possible. Have a great day!',
    'out_of_reach'
);

-- Verify the message was inserted
SELECT 
    id,
    message_type,
    message,
    LENGTH(message) as message_length
FROM general_messages 
WHERE message_type = 'out_of_reach';

-- Show all message types for reference
SELECT 
    message_type,
    COUNT(*) as count,
    MAX(LENGTH(message)) as max_message_length
FROM general_messages 
GROUP BY message_type
ORDER BY message_type;
